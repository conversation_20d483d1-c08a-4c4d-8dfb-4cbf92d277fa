/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: #212121;
  color: #ececec;
  height: 100vh;
  overflow: hidden;
}

/* Main app container */
.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
}

/* Sidebar styles */
.sidebar {
  width: 260px;
  background-color: #171717;
  border-right: 1px solid #4d4d4f;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
}

.sidebar.collapsed {
  width: 0;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #4d4d4f;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #ececec;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #8e8ea0;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sidebar-toggle:hover {
  background-color: #2d2d30;
}

.servers-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.active-server {
  font-size: 12px;
  color: #8e8ea0;
  margin-bottom: 12px;
  padding: 0 8px;
}

.server-button {
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 4px;
  background: none;
  border: none;
  color: #ececec;
  text-align: left;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.server-button:hover {
  background-color: #2d2d30;
}

.server-button.active {
  background-color: #10a37f;
  color: white;
}

/* Toggle button for collapsed sidebar */
.sidebar-toggle-collapsed {
  position: fixed;
  top: 16px;
  left: 16px;
  z-index: 1000;
  background-color: #171717;
  border: 1px solid #4d4d4f;
  color: #8e8ea0;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.sidebar-toggle-collapsed:hover {
  background-color: #2d2d30;
}

/* Main content area */
.main-part {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #212121;
  position: relative;
}

/* Chat header */
.chat-header {
  padding: 16px 24px;
  border-bottom: 1px solid #4d4d4f;
  background-color: #212121;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #ececec;
  margin: 0;
}

/* Chat messages container */
.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: #212121;
}

.chat-container::-webkit-scrollbar-thumb {
  background: #4d4d4f;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #6e6e80;
}

/* Message styles */
.message {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.message.user {
  align-self: flex-end;
}

.message.assistant {
  align-self: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.message-avatar.user {
  background-color: #10a37f;
  color: white;
}

.message-avatar.assistant {
  background-color: #ab68ff;
  color: white;
}

.message-role {
  font-size: 14px;
  font-weight: 600;
  color: #ececec;
}

.message-content {
  background-color: #2d2d30;
  padding: 16px;
  border-radius: 12px;
  line-height: 1.6;
}

.message-content p {
  margin-bottom: 12px;
}

.message-content p:last-child {
  margin-bottom: 0;
}

/* Tool calls styling */
.tool-calls-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #1a1a1a;
  border-radius: 8px;
  border-left: 4px solid #10a37f;
}

.tool-calls-title {
  font-size: 14px;
  font-weight: 600;
  color: #10a37f;
  margin-bottom: 12px;
}

.tool-call-container {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #2d2d30;
  border-radius: 6px;
}

.tool-call-container:last-child {
  margin-bottom: 0;
}

.tool-call-title {
  font-size: 12px;
  font-weight: 600;
  color: #8e8ea0;
  margin-bottom: 8px;
}

.tool-call-function,
.tool-call-arguments {
  font-size: 13px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  background-color: #1a1a1a;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  overflow-x: auto;
}

.tool-call-arguments:last-child {
  margin-bottom: 0;
}

/* Input container */
.input-container {
  padding: 24px;
  border-top: 1px solid #4d4d4f;
  background-color: #212121;
}

.input-wrapper {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.input-box {
  flex: 1;
  background-color: #2d2d30;
  border: 1px solid #4d4d4f;
  border-radius: 12px;
  padding: 12px 16px;
  color: #ececec;
  font-size: 16px;
  line-height: 1.5;
  resize: none;
  min-height: 24px;
  max-height: 200px;
  outline: none;
  transition: border-color 0.2s;
}

.input-box:focus {
  border-color: #10a37f;
}

.input-box::placeholder {
  color: #8e8ea0;
}

.send-button {
  background-color: #10a37f;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  transition: background-color 0.2s;
  min-width: 60px;
}

.send-button:hover:not(:disabled) {
  background-color: #0d8f6f;
}

.send-button:disabled {
  background-color: #4d4d4f;
  cursor: not-allowed;
}

/* Empty state */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #8e8ea0;
}

.empty-state-content h2 {
  font-size: 24px;
  margin-bottom: 8px;
  color: #ececec;
}

.empty-state-content p {
  font-size: 16px;
  line-height: 1.5;
}

/* Error state */
.error-container {
  padding: 24px;
  text-align: center;
  color: #ff6b6b;
  background-color: #2d1b1b;
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  margin: 24px;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebar.collapsed {
    width: 100%;
    transform: translateX(-100%);
  }

  .main-part {
    width: 100%;
  }

  .sidebar-toggle-collapsed {
    display: block;
  }
}

@media (min-width: 769px) {
  .sidebar-toggle-collapsed {
    display: none;
  }
}
