#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.title {
  font-size: 2.5rem;
  color: #646cff;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.input-container {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.input-box {
  width: 60%;
  padding: 0.8rem;
  border-radius: 8px;
  border: 1px solid #646cff;
  background-color: #1a1a1a;
  color: white;
  font-size: 1rem;
  transition: border-color 0.25s;
}

.input-box:focus {
  outline: none;
  border-color: #535bf2;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2);
}

.send-button {
  background-color: #646cff;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  font-weight: bold;
  transition: background-color 0.25s;
}

.send-button:hover {
  background-color: #535bf2;
}

.response-container {
  background-color: #1a1a1a;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
  text-align: left;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.input-text {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.response-text {
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.tool-call-container {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.tool-call-function {
  color: #10b981;
  font-family: monospace;
  margin-bottom: 0.5rem;
}

.tool-call-arguments {
  color: #94a3b8;
  font-family: monospace;
  white-space: pre-wrap;
}

.indicator {
  color: #646cff;
  font-weight: bold;
  margin-bottom: 0.5rem;
}
