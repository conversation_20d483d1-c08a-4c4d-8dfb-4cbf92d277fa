import "./App.css";
import { useState } from "react";
import type { ChatRequest, ChatResponse } from "./types";
import { stdioServer, BASE_URL, mathServer, uselessServer } from "./constants";
import ReactMarkdown from "react-markdown";

// TODO:
// 1. fix the UI (css)
// 4. add specific servers that are not dummys

function App() {
  const servers = [stdioServer, mathServer, uselessServer];
  const [input, setInput] = useState("");
  const [history, setHistory] = useState<Array<Record<string, any>> | null>(
    null
  );
  const [response, setResponse] = useState<ChatResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [server, setServer] = useState(stdioServer);

  const handleSend = () => {
    if (!input.trim()) return;
    const newRequest: ChatRequest = {
      input,
      server,
      history,
    };

    const post = async (request: ChatRequest) => {
      try {
        const response = await fetch(`${BASE_URL}/chat/request`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(request),
        });

        if (!response.ok) {
          setError(
            `HTTP error! status: ${response.status} ${response.statusText}`
          );
          return;
        }
        const data: ChatResponse = await response.json();
        setResponse(data);
        setHistory(data.history);
      } catch (error) {
        setError(`Error: ${error}`);
      } finally {
        setInput("");
      }
    };

    post(newRequest);
  };

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="app-container">
      <div className="sidebar">
        <h2 className="sidebar-title">Servers</h2>
        <h3 className="active-server">Current Server: {server.name}</h3>
        {servers.map((server) => (
          <button
            key={server.key}
            className="server-button"
            onClick={() => setServer(server)}
          >
            {server.name}
          </button>
        ))}
      </div>
      <div className="main-part">
        <h1 className="title">MCP Chat Interface</h1>
        {(response && (
          <div className="response-container">
            <h3 className="indicator">User Input</h3>
            <p className="input-text">{response.user_input}</p>
            <h3 className="indicator">LLM Response</h3>
            <p className="response-text">
              <ReactMarkdown>{response.model_response}</ReactMarkdown>
            </p>
            {response.tool_calls &&
              Object.keys(response.tool_calls).length > 0 && (
                <>
                  <h3 className="indicator">Tool Calls</h3>
                  {response.tool_calls.map((toolCall, index) => (
                    <div className="tool-call-container" key={index}>
                      <h4 className="tool-call-title">Tool Call {index + 1}</h4>
                      <p className="tool-call-function">
                        LLM request tool call `{toolCall.tool_name}`` with args
                        `{JSON.stringify(toolCall.tool_args, null, 2)}`
                      </p>
                      <p className="tool-call-arguments">
                        Tool response: `{toolCall.tool_response}`
                      </p>
                    </div>
                  ))}
                </>
              )}
          </div>
        )) ?? (
          <>
            <br></br>
            <br></br>
            <br></br>
            <br></br>
          </>
        )}
        <div className="input-container">
          <input
            type="text"
            autoFocus={true}
            value={input}
            className="input-box"
            onChange={(e) => setInput(e.target.value)}
            onSubmit={(e) => {
              e.preventDefault();
              handleSend();
            }}
          />
          <button className="send-button" onClick={handleSend}>
            Send
          </button>
        </div>
      </div>
    </div>
  );
}

export default App;
