import "./App.css";
import { useState } from "react";
import type { ChatRequest, ChatResponse } from "./types";
import { stdioServer, BASE_URL, mathServer, uselessServer } from "./constants";
import ReactMarkdown from "react-markdown";

// TODO:
// 1. fix the UI (css)
// 4. add specific servers that are not dummys

function App() {
  const servers = [stdioServer, mathServer, uselessServer];
  const [input, setInput] = useState("");
  const [history, setHistory] = useState<Array<Record<string, any>> | null>(
    null
  );
  const [response, setResponse] = useState<ChatResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [server, setServer] = useState(stdioServer);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSend = () => {
    if (!input.trim() || isLoading) return;

    const newRequest: ChatRequest = {
      input,
      server,
      history,
    };

    const post = async (request: ChatRequest) => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`${BASE_URL}/chat/request`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(request),
        });

        if (!response.ok) {
          setError(
            `HTTP error! status: ${response.status} ${response.statusText}`
          );
          return;
        }
        const data: ChatResponse = await response.json();
        setResponse(data);
        setHistory(data.history);
      } catch (error) {
        setError(`Error: ${error}`);
      } finally {
        setIsLoading(false);
        setInput("");
      }
    };

    post(newRequest);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  if (error) {
    return (
      <div className="app-container">
        <div className="error-container">
          <h2>Error</h2>
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            style={{
              marginTop: "16px",
              padding: "8px 16px",
              backgroundColor: "#10a37f",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="app-container">
      {/* Collapsed sidebar toggle button */}
      {sidebarCollapsed && (
        <button
          className="sidebar-toggle-collapsed"
          onClick={() => setSidebarCollapsed(false)}
          aria-label="Open sidebar"
        >
          ☰
        </button>
      )}

      {/* Sidebar */}
      <div className={`sidebar ${sidebarCollapsed ? "collapsed" : ""}`}>
        <div className="sidebar-header">
          <h2 className="sidebar-title">Servers</h2>
          <button
            className="sidebar-toggle"
            onClick={() => setSidebarCollapsed(true)}
            aria-label="Close sidebar"
          >
            ←
          </button>
        </div>
        <div className="servers-container">
          <h3 className="active-server">Current: {server.name}</h3>
          {servers.map((serverOption) => (
            <button
              key={serverOption.key}
              className={`server-button ${
                server.key === serverOption.key ? "active" : ""
              }`}
              onClick={() => setServer(serverOption)}
            >
              {serverOption.name}
            </button>
          ))}
        </div>
      </div>

      {/* Main content area */}
      <div className="main-part">
        <div className="chat-header">
          <h1 className="title">MCP Chat Interface</h1>
        </div>

        <div className="chat-container">
          {response ? (
            <div className="message assistant">
              <div className="message-header">
                <div className="message-avatar user">U</div>
                <span className="message-role">You</span>
              </div>
              <div className="message-content">
                <p>{response.user_input}</p>
              </div>
            </div>
          ) : null}

          {response ? (
            <div className="message assistant">
              <div className="message-header">
                <div className="message-avatar assistant">AI</div>
                <span className="message-role">Assistant</span>
              </div>
              <div className="message-content">
                <ReactMarkdown>{response.model_response}</ReactMarkdown>

                {response.tool_calls && response.tool_calls.length > 0 && (
                  <div className="tool-calls-section">
                    <h3 className="tool-calls-title">Tool Calls</h3>
                    {response.tool_calls.map((toolCall, index) => (
                      <div className="tool-call-container" key={index}>
                        <h4 className="tool-call-title">
                          Tool Call {index + 1}
                        </h4>
                        <div className="tool-call-function">
                          LLM request tool call `{toolCall.tool_name}` with
                          args:
                          <br />
                          {JSON.stringify(toolCall.tool_args, null, 2)}
                        </div>
                        <div className="tool-call-arguments">
                          Tool response: {toolCall.tool_response}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-state-content">
                <h2>Welcome to MCP Chat</h2>
                <p>Select a server from the sidebar and start chatting!</p>
              </div>
            </div>
          )}
        </div>

        <div className="input-container">
          <div className="input-wrapper">
            <input
              type="text"
              autoFocus={true}
              value={input}
              className="input-box"
              placeholder="Type your message here..."
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isLoading}
            />
            <button
              className="send-button"
              onClick={handleSend}
              disabled={isLoading || !input.trim()}
            >
              {isLoading ? "..." : "Send"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
