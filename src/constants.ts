import type { MCPServer } from "./types";

const stdioServer: MCPServer = {
  key: "stdio",
  name: "stdio",
  type: "local",
  command: "uv",
  args: [
    "run",
    "/Users/<USER>/Documents/[32-dev] project/Zhanya/Cooperator/backend/server/stdio.py",
  ],
  is_active: false,
};

const mathServer: MCPServer = {
  key: "math",
  name: "math",
  type: "local",
  command: "uv",
  args: [
    "run",
    "/Users/<USER>/Documents/[32-dev] project/Zhanya/Cooperator/backend/server/server_math.py",
  ],
  is_active: false,
};

const uselessServer: MCPServer = {
  key: "useless",
  name: "useless",
  type: "local",
  command: "uv",
  args: [
    "run",
    "/Users/<USER>/Documents/[32-dev] project/Zhanya/Cooperator/backend/server/useless.py",
  ],
  is_active: false,
};

const BASE_URL = "http://127.0.0.1:8000";

export { stdioServer, BASE_URL, mathServer, uselessServer };
