interface ChatRequest {
  input: string;
  limit?: number;
  server: MCPServer;
  history?: Array<Record<string, any>> | null;
}

interface MCPServer {
  key: string;
  name?: string;
  type?: "local" | "remote";
  url?: string;
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  headers?: Record<string, string>;
  proxy?: string;
  is_active: boolean;
}

interface ChatResponse {
  tool_calls?: Array<ToolCall>;
  user_input: string;
  model_response?: string;
  history: Array<Record<string, any>>;
  error?: string;
}

interface ToolCall {
  tool_name: string;
  tool_args: Record<string, any>;
  tool_response?: any;
}

export type { ChatRequest, MCPServer, ChatResponse };
